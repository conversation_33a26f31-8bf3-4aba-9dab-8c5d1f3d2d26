# Weather Typing Cluster Validation Framework

A comprehensive validation framework for evaluating weather typing cluster solutions with specialized metrics for atmospheric science applications.

## Overview

This framework provides robust validation of clustering results for weather typing applications, addressing common issues like k=2 bias, temporal stability, and physical meaningfulness of weather patterns. It includes both traditional clustering metrics and specialized metrics designed for atmospheric time series data.

## Features

### Core Validation Metrics
- **Traditional Metrics**: Silhouette Score, Calinski-Harabasz Index, Davies-Bouldin Index
- **Bias-Corrected Metrics**: Penalized versions that address k=2 bias and overfitting
- **Advanced Metrics**: Dunn Index, Gap Statistic, Dynamic Validity Index (DVI)

### Weather-Specific Metrics
- **Explained Variance per Weather Type**: Physical meaningfulness assessment
- **Temporal Stability**: ARI-based stability across time windows
- **Seasonal Stability**: Consistency across seasons
- **Persistence Analysis**: Weather type duration and transition patterns
- **Procrustes Analysis**: Centroid stability over time

### Visualization & Reporting
- **Comprehensive Plotting**: 4×4 grid with all validation metrics
- **Weather Type Characteristics**: Size, variability, and explained variance analysis
- **Decision Matrix**: Multi-criteria heatmap for k selection
- **Consensus Recommendations**: Evidence-based k selection
- **Detailed Reports**: Scientific documentation with rankings and analysis

## Installation

### Dependencies
```bash
pip install numpy pandas xarray matplotlib seaborn scikit-learn scipy
```

### Files Required
- `clustering_metrics.py`: Main validation framework
- Cluster results file (NetCDF format with k=2, k=3, ... variables)
- EOF-reduced data array (optional but recommended)

## Quick Start

### Basic Usage
```python
from clustering_metrics import WeatherTypingValidator

# Initialize validator with cluster results
validator = WeatherTypingValidator("cluster_results.nc")

# Option 1: With EOF-reduced data (recommended)
validator.reduced_data = your_eof_data  # Shape: (n_samples, n_features)

# Option 2: Setup with clusterer configuration
# validator.setup_with_clusterer(
#     latlon_path="latlon.nc",
#     variable_paths={"slp": "slp_data.nc", "z500": "z500_data.nc"},
#     variable_names={"slp": "msl", "z500": "z"},
#     levels={"slp": None, "z500": 500},
#     retain_variance=0.85
# )

# Calculate all validation metrics
results = validator.calculate_all_metrics(k_range=range(2, 21))

# Create comprehensive validation plots
validator.plot_comprehensive_metrics(figsize=(20, 16), save_path="validation_plots.png")

# Generate detailed report
validator.generate_comprehensive_report(output_file="validation_report.txt")
```

## Detailed Usage

### 1. Data Preparation

#### Cluster Results Format
Your cluster results should be in NetCDF format with variables named `k=2`, `k=3`, etc.:
```python
import xarray as xr
ds = xr.Dataset({
    'k=2': (['time'], labels_k2),
    'k=3': (['time'], labels_k3),
    # ... more k values
})
ds.to_netcdf("cluster_results.nc")
```

#### EOF-Reduced Data
For distance-based metrics, provide EOF-reduced data:
```python
# Example: EOF reduction to retain 85% variance
from sklearn.decomposition import PCA
pca = PCA(n_components=0.85)
reduced_data = pca.fit_transform(your_weather_data)
validator.reduced_data = reduced_data
```

### 2. Comprehensive Analysis

#### Calculate All Metrics
```python
# With date information for seasonal analysis
import pandas as pd
dates = pd.date_range('2000-01-01', periods=len(labels), freq='D')
results = validator.calculate_all_metrics(k_range=range(2, 21), dates=dates)

# View results
print(results.head())
print(f"Available metrics: {list(results.columns)}")
```

#### Weather Type Characteristics Analysis
```python
# Plot WT characteristics across k values
validator.plot_wt_characteristics(k_values=range(2, 16), figsize=(15, 10))

# Detailed analysis for specific k
validator.print_wt_summary(k=8)

# Get programmatic access to WT details
analysis = validator.get_wt_detailed_analysis(k=8)
print(f"Weather types for k=8: {analysis['weather_types'].keys()}")
```

### 3. Visualization Options

#### Standard Plotting (3×3 grid)
```python
validator.plot_metrics(figsize=(15, 12), save_path="standard_plots.png")
```

#### Comprehensive Plotting (4×4 grid with advanced metrics)
```python
validator.plot_comprehensive_metrics(figsize=(20, 16), save_path="comprehensive_plots.png")
```

#### Custom Analysis
```python
# Access individual metrics
optimal_k_silhouette = results.loc[results['penalized_silhouette'].idxmax(), 'k']
optimal_k_stability = results.loc[results['temporal_ari_mean'].idxmax(), 'k']

# Plot specific metrics
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 4))
plt.subplot(1, 3, 1)
plt.plot(results['k'], results['explained_variance_ratio'], 'o-')
plt.title('Explained Variance Ratio')
plt.xlabel('k')
plt.ylabel('Explained Variance')

plt.subplot(1, 3, 2)
plt.plot(results['k'], results['overall_persistence'], 'o-')
plt.title('Weather Type Persistence')
plt.xlabel('k')
plt.ylabel('Persistence')

plt.subplot(1, 3, 3)
plt.plot(results['k'], results['seasonal_stability_mean'], 'o-')
plt.title('Seasonal Stability')
plt.xlabel('k')
plt.ylabel('Seasonal ARI')
plt.tight_layout()
plt.show()
```

## Understanding the Metrics

### Distance-Based Metrics (require reduced data)
- **Penalized Silhouette**: Bias-corrected silhouette score (higher = better)
- **Dunn Index**: Inter/intra-cluster distance ratio (higher = better)
- **Explained Variance Ratio**: Variance explained by clustering (higher = better)
- **Gap Statistic**: Comparison with random data (higher = better)

### Temporal Stability Metrics (work without reduced data)
- **Temporal ARI**: Stability across time windows (higher = better)
- **Seasonal Stability**: Consistency across seasons (higher = better)
- **Frequency Stability**: CV of cluster frequencies (lower = better)
- **Overall Persistence**: Weather type persistence (higher = better)

### Quality Assessment Metrics
- **Dynamic Validity Index (DVI)**: Temporal clustering quality (higher = better)
- **Procrustes Distance**: Centroid stability (lower = better)
- **Complexity Penalty**: AIC-like penalty (lower = better)

## Interpretation Guidelines

### Choosing Optimal k
1. **Consensus Approach**: Use `generate_comprehensive_report()` for evidence-based recommendation
2. **Physical Meaningfulness**: Prioritize explained variance and persistence metrics
3. **Stability**: Ensure high temporal and seasonal stability
4. **Balance**: Avoid overfitting (very high k) or oversimplification (k=2 bias)

### Red Flags
- Very low explained variance ratio (< 0.3)
- Poor temporal stability (ARI < 0.3)
- Extreme cluster size imbalance
- Very low persistence (< 0.2)

### Best Practices
- Always use EOF-reduced data when available
- Consider multiple metrics, not just one
- Validate results with domain expertise
- Check seasonal consistency for climate applications
- Document methodology and results

## Advanced Features

### Custom Metric Calculation
```python
# Calculate specific metrics manually
labels = validator.ds['k=8'].values
dunn_score = validator.calculate_dunn_index(labels, validator.reduced_data)
persistence_metrics, overall_persistence = validator.calculate_persistence_metrics(labels)
```

### Batch Processing
```python
# Process multiple datasets
datasets = ["region1_clusters.nc", "region2_clusters.nc", "region3_clusters.nc"]
results_summary = {}

for dataset in datasets:
    validator = WeatherTypingValidator(dataset, reduced_data=eof_data)
    results = validator.calculate_all_metrics()
    report = validator.generate_comprehensive_report(f"{dataset}_report.txt")
    results_summary[dataset] = results
```

## Troubleshooting

### Common Issues
1. **"No reduced data available"**: Provide EOF-reduced data for distance-based metrics
2. **Dimension mismatch**: Ensure cluster labels and reduced data have same number of samples
3. **All NaN results**: Check data format and ensure cluster labels are valid integers
4. **Memory issues**: Reduce k_range or use smaller datasets for testing

### Performance Tips
- Use k_range=range(2, 16) for initial exploration
- Provide reduced data with ~50-200 components for optimal performance
- Use dates parameter only when seasonal analysis is needed

## Citation

If you use this framework in your research, please cite:
```
Weather Typing Cluster Validation Framework
[Your citation information here]
```

## Example Workflow

### Complete Analysis Pipeline
```python
import numpy as np
import pandas as pd
import xarray as xr
from clustering_metrics import WeatherTypingValidator

# 1. Load your data and cluster results
validator = WeatherTypingValidator("CONUS_ERA5_clusters.nc")

# 2. Set up EOF-reduced data (recommended)
# Assuming you have your atmospheric data in reduced form
validator.reduced_data = eof_reduced_data  # Shape: (n_days, n_components)

# 3. Prepare date information for seasonal analysis
dates = pd.date_range('1979-01-01', '2020-12-31', freq='D')

# 4. Calculate comprehensive validation metrics
print("Calculating validation metrics...")
results = validator.calculate_all_metrics(k_range=range(2, 21), dates=dates)

# 5. Create visualizations
print("Creating comprehensive plots...")
validator.plot_comprehensive_metrics(figsize=(20, 16), save_path="validation_comprehensive.png")

print("Creating weather type characteristics plots...")
validator.plot_wt_characteristics(k_values=range(2, 16), figsize=(15, 10))

# 6. Generate detailed analysis for top candidates
print("Analyzing top k candidates...")
# Get consensus recommendations first
report = validator.generate_comprehensive_report("validation_report.txt")

# Analyze specific k values in detail
for k in [6, 7, 8, 9, 10]:  # Common optimal range for weather typing
    print(f"\n--- Analysis for k={k} ---")
    validator.print_wt_summary(k)

# 7. Export results for further analysis
results.to_csv("validation_metrics.csv", index=False)
print("Results saved to validation_metrics.csv")
```

### Interpreting Results

#### Example Output Interpretation
```
CONSENSUS RECOMMENDATION:
Optimal k = 8

This choice is supported by 5 different validation metrics:
- Penalized Silhouette: k=8 (0.342)
- Explained Variance: k=8 (0.678)
- Temporal ARI: k=8 (0.456)
- Seasonal Stability: k=8 (0.423)
- Overall Persistence: k=8 (0.334)

DETAILED ANALYSIS FOR k=8:
Number of Weather Types: 8
Mean WT Size: 45.6 days
Size Variability (CV): 0.234
Mean Explained Variance: 0.678
```

**What this tells us:**
- k=8 has strong consensus support across multiple validation criteria
- Weather types explain 67.8% of atmospheric variance (good physical meaning)
- Average weather type lasts ~46 days (realistic for synoptic patterns)
- Moderate size variability (some WTs more common than others, which is expected)
- Good temporal and seasonal stability

## Metric Reference Guide

### Metric Ranges and Interpretation

| Metric | Range | Good Values | Interpretation |
|--------|-------|-------------|----------------|
| Penalized Silhouette | [-1, 1] | > 0.3 | Cluster separation quality |
| Explained Variance Ratio | [0, 1] | > 0.5 | Physical meaningfulness |
| Temporal ARI | [0, 1] | > 0.3 | Temporal consistency |
| Seasonal Stability | [0, 1] | > 0.2 | Cross-seasonal consistency |
| Overall Persistence | [0, 1] | > 0.2 | Weather pattern persistence |
| Dunn Index | [0, ∞] | > 1.0 | Cluster separation |
| Frequency Stability (CV) | [0, ∞] | < 0.5 | Cluster size consistency |

### Weather Typing Specific Guidelines

#### Optimal k Selection Criteria
1. **Physical Meaningfulness** (Priority 1)
   - Explained variance ratio > 0.5
   - Realistic weather type persistence (0.2-0.6)
   - Reasonable cluster sizes (not too imbalanced)

2. **Temporal Stability** (Priority 2)
   - Temporal ARI > 0.3
   - Seasonal stability > 0.2
   - Low frequency variability (CV < 0.5)

3. **Statistical Quality** (Priority 3)
   - High penalized silhouette score
   - Good separation (Dunn Index > 1.0)
   - Consensus across multiple metrics

#### Common k Values for Weather Typing
- **k=4-6**: Suitable for large-scale circulation patterns
- **k=6-10**: Most common for regional weather typing
- **k=10-15**: Detailed local weather patterns
- **k>15**: Risk of overfitting, use with caution

## API Reference

### WeatherTypingValidator Class

#### Initialization
```python
WeatherTypingValidator(dataset_path, reduced_data=None)
```
- `dataset_path`: Path to NetCDF file with cluster results
- `reduced_data`: Optional EOF-reduced data array (n_samples, n_features)

#### Key Methods

##### Data Setup
- `load_reduced_data_from_clusterer(clusterer)`: Load data from AtmosClusterer
- `setup_with_clusterer(...)`: Setup with clusterer configuration

##### Metric Calculation
- `calculate_all_metrics(k_range, dates=None)`: Calculate all validation metrics
- `calculate_explained_variance_per_wt(labels, data)`: WT-specific variance analysis
- `calculate_persistence_metrics(labels)`: Weather type persistence analysis
- `calculate_seasonal_stability(labels, dates)`: Cross-seasonal consistency

##### Analysis & Reporting
- `get_wt_detailed_analysis(k)`: Detailed analysis for specific k
- `print_wt_summary(k)`: Print formatted summary for k
- `generate_comprehensive_report(output_file)`: Create validation report

##### Visualization
- `plot_metrics(figsize, save_path)`: Standard 3×3 metric plots
- `plot_comprehensive_metrics(figsize, save_path)`: Advanced 4×4 plots
- `plot_wt_characteristics(k_values, figsize)`: WT characteristics analysis

## Contributing

### Adding New Metrics
To add a new validation metric:

1. **Implement the metric calculation**:
```python
def calculate_new_metric(self, labels, data=None):
    """Calculate your new metric"""
    # Your implementation here
    return metric_value
```

2. **Add to calculate_all_metrics**:
```python
# In the metrics dictionary
'new_metric': [],

# In the calculation loop
new_metric_val = self.calculate_new_metric(labels, self.reduced_data)
metrics['new_metric'].append(new_metric_val)
```

3. **Update plotting and reporting functions** to include the new metric

### Reporting Issues
Please report issues with:
- Sample data that reproduces the problem
- Full error traceback
- System information (Python version, package versions)

## License

[Your license information here]
