import numpy as np
import pandas as pd
import xarray as xr
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
from sklearn.cluster import KMeans
from scipy.spatial.distance import pdist, squareform
from scipy.stats import chi2
from scipy.spatial import procrustes
from scipy.optimize import linear_sum_assignment
import warnings
warnings.filterwarnings('ignore')

class WeatherTypingValidator:
    def __init__(self, dataset_path, reduced_data=None):
        """
        Initialize the validator with weather data

        Parameters:
        -----------
        dataset_path : str
            Path to the netCDF file containing cluster results
        reduced_data : numpy.ndarray, optional
            EOF-reduced data array from AtmosClusterer (samples x features)
        """
        self.ds = xr.open_dataset(dataset_path)
        self.reduced_data = reduced_data
        self.results = {}

    def load_reduced_data_from_clusterer(self, clusterer):
        """Load reduced data directly from AtmosClusterer instance"""
        self.reduced_data = clusterer.run()
        print(f"Loaded reduced data with shape: {self.reduced_data.shape}")

    def setup_with_clusterer(self, latlon_path, variable_paths, variable_names, levels, retain_variance=0.85):
        """
        Setup validator with AtmosClusterer configuration

        Parameters:
        -----------
        latlon_path : str
            Path to lat/lon reference file
        variable_paths : dict
            Dictionary mapping variable names to file paths
        variable_names : dict
            Dictionary mapping variable keys to netCDF variable names
        levels : dict
            Dictionary mapping variables to pressure levels (None for surface)
        retain_variance : float
            Fraction of variance to retain in EOF analysis
        """
        from your_clusterer_module import AtmosClusterer  # Adjust import as needed

        clusterer = AtmosClusterer(
            latlon_path=latlon_path,
            variable_paths=variable_paths,
            variable_names=variable_names,
            levels=levels,
            retain_variance=retain_variance
        )

        # Run dimensionality reduction
        self.reduced_data = clusterer.run()
        print(f"Loaded reduced data with shape: {self.reduced_data.shape}")

    def load_original_data(self, data_path, var_name):
        """Load original weather data for distance-based metrics (deprecated - use reduced_data instead)"""
        print("Warning: Using load_original_data is deprecated. Use reduced_data from AtmosClusterer instead.")
        self.weather_data = xr.open_dataset(data_path)[var_name]
        # Flatten spatial dimensions if needed
        if len(self.weather_data.dims) > 2:
            self.weather_data = self.weather_data.stack(space=self.weather_data.dims[1:])

    def calculate_wcss(self, labels, data=None):
        """Calculate Within-Cluster Sum of Squares"""
        if data is None:
            return np.nan

        wcss = 0
        for k in np.unique(labels):
            if k >= 0:  # Exclude noise points if any
                cluster_points = data[labels == k]
                centroid = np.mean(cluster_points, axis=0)
                wcss += np.sum((cluster_points - centroid) ** 2)
        return wcss

    def calculate_penalized_metrics(self, labels, data, k):
        """Calculate metrics with penalties for extreme k values"""
        if data is None:
            return np.nan, np.nan, np.nan, np.nan

        n_samples = len(labels)
        unique_labels = np.unique(labels[labels >= 0])
        actual_k = len(unique_labels)

        # Base metrics
        try:
            sil_score = silhouette_score(data, labels) if actual_k > 1 else -1
            ch_score = calinski_harabasz_score(data, labels) if actual_k > 1 else 0
            db_score = davies_bouldin_score(data, labels) if actual_k > 1 else np.inf
        except:
            return np.nan, np.nan, np.nan, np.nan

        # Penalty factors
        # Penalize very low k (oversimplification)
        low_k_penalty = max(0, (5 - k) / 10) if k < 5 else 0

        # Penalize very high k (overfitting)
        high_k_penalty = max(0, (k - 25) / 10) if k > 25 else 0

        # Penalize unbalanced clusters
        cluster_sizes = [np.sum(labels == label) for label in unique_labels]
        balance_ratio = min(cluster_sizes) / max(cluster_sizes) if cluster_sizes else 0
        balance_penalty = (1 - balance_ratio) * 0.5

        # Apply penalties
        penalized_sil = sil_score - low_k_penalty - high_k_penalty - balance_penalty
        penalized_ch = ch_score * (1 - low_k_penalty - high_k_penalty - balance_penalty)
        penalized_db = db_score * (1 + low_k_penalty + high_k_penalty + balance_penalty)

        # Complexity penalty (AIC-like)
        complexity_penalty = (k * np.log(n_samples)) / n_samples

        return penalized_sil, penalized_ch, penalized_db, complexity_penalty

    def calculate_procrustes_stability(self, labels_list, data, window_size=365):
        """
        Calculate Procrustes analysis for cluster centroid stability
        Measures how much cluster centroids move/rotate between time periods
        """
        if data is None or len(labels_list) < 2:
            return np.nan, np.nan

        n_samples = len(labels_list[0])
        if n_samples < window_size * 2:
            return np.nan, np.nan

        procrustes_distances = []

        # Compare cluster centroids between time windows
        n_windows = n_samples // window_size
        if n_windows < 2:
            return np.nan, np.nan

        for k_idx, labels in enumerate(labels_list):
            if k_idx == 0:  # Skip k=2 comparison
                continue

            window_distances = []
            unique_labels = np.unique(labels[labels >= 0])

            if len(unique_labels) < 2:
                continue

            for i in range(n_windows - 1):
                # Get centroids for consecutive time windows
                window1_start = i * window_size
                window1_end = (i + 1) * window_size
                window2_start = (i + 1) * window_size
                window2_end = min((i + 2) * window_size, n_samples)

                if window2_end - window2_start < window_size // 2:
                    continue

                # Calculate centroids for each window
                centroids1 = []
                centroids2 = []

                for label in unique_labels:
                    # Window 1 centroids
                    mask1 = labels[window1_start:window1_end] == label
                    if np.sum(mask1) > 0:
                        centroid1 = np.mean(data[window1_start:window1_end][mask1], axis=0)
                        centroids1.append(centroid1)

                    # Window 2 centroids
                    mask2 = labels[window2_start:window2_end] == label
                    if np.sum(mask2) > 0:
                        centroid2 = np.mean(data[window2_start:window2_end][mask2], axis=0)
                        centroids2.append(centroid2)

                # Procrustes analysis between centroid sets
                if len(centroids1) >= 2 and len(centroids2) >= 2 and len(centroids1) == len(centroids2):
                    try:
                        centroids1 = np.array(centroids1)
                        centroids2 = np.array(centroids2)

                        # Perform Procrustes analysis
                        _, _, disparity = procrustes(centroids1, centroids2)
                        window_distances.append(disparity)
                    except:
                        continue

            if window_distances:
                procrustes_distances.extend(window_distances)

        if procrustes_distances:
            return np.mean(procrustes_distances), np.std(procrustes_distances)
        return np.nan, np.nan

    def plot_comprehensive_metrics(self, figsize=(20, 16), save_path=None):
        """Create comprehensive plots of all validation metrics"""

        if self.results.empty:
            print("No results to plot. Run calculate_all_metrics() first.")
            return

        fig, axes = plt.subplots(4, 4, figsize=figsize)
        fig.suptitle('Weather Typing Cluster Validation Metrics', fontsize=18, fontweight='bold')

        # Plot 1: Penalized Silhouette Score (addresses 2-cluster bias)
        axes[0,0].plot(self.results['k'], self.results['penalized_silhouette'], 'bo-', linewidth=2, markersize=4, label='Penalized')
        axes[0,0].plot(self.results['k'], self.results['silhouette'], 'b--', alpha=0.5, label='Original')
        axes[0,0].set_title('Penalized Silhouette Score\n(Higher is Better, Bias Corrected)')
        axes[0,0].set_xlabel('Number of Clusters (k)')
        axes[0,0].set_ylabel('Penalized Silhouette')
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].legend()

        # Plot 2: Penalized Calinski-Harabasz
        axes[0,1].plot(self.results['k'], self.results['penalized_ch'], 'go-', linewidth=2, markersize=4, label='Penalized')
        axes[0,1].plot(self.results['k'], self.results['calinski_harabasz'], 'g--', alpha=0.5, label='Original')
        axes[0,1].set_title('Penalized Calinski-Harabasz\n(Higher is Better, Bias Corrected)')
        axes[0,1].set_xlabel('Number of Clusters (k)')
        axes[0,1].set_ylabel('Penalized CH Index')
        axes[0,1].grid(True, alpha=0.3)
        axes[0,1].legend()

        # Plot 3: Dynamic Validity Index (Overall)
        axes[0,2].plot(self.results['k'], self.results['dvi_overall'], 'mo-', linewidth=2, markersize=4)
        axes[0,2].set_title('Dynamic Validity Index\n(Higher is Better)')
        axes[0,2].set_xlabel('Number of Clusters (k)')
        axes[0,2].set_ylabel('DVI Overall')
        axes[0,2].grid(True, alpha=0.3)

        # Plot 4: DVI Components
        valid_dvi = ~np.isnan(self.results['dvi_individual'])
        if valid_dvi.any():
            axes[0,3].plot(self.results['k'][valid_dvi], self.results['dvi_individual'][valid_dvi],
                          'r-', linewidth=2, label='Individual Quality')
            axes[0,3].plot(self.results['k'][valid_dvi], self.results['dvi_temporal'][valid_dvi],
                          'b-', linewidth=2, label='Temporal Consistency')
        axes[0,3].set_title('DVI Components')
        axes[0,3].set_xlabel('Number of Clusters (k)')
        axes[0,3].set_ylabel('DVI Component Score')
        axes[0,3].grid(True, alpha=0.3)
        axes[0,3].legend()

        # Plot 5: Procrustes Analysis (Centroid Stability)
        if not np.isnan(self.results['procrustes_mean'].iloc[0]):
            axes[1,0].errorbar(self.results['k'], self.results['procrustes_mean'],
                              yerr=self.results['procrustes_std'],
                              fmt='co-', linewidth=2, markersize=4, capsize=5)
        axes[1,0].set_title('Procrustes Centroid Stability\n(Lower is Better)')
        axes[1,0].set_xlabel('Number of Clusters (k)')
        axes[1,0].set_ylabel('Procrustes Distance')
        axes[1,0].grid(True, alpha=0.3)

        # Plot 6: Temporal ARI Stability
        valid_ari = ~np.isnan(self.results['temporal_ari_mean'])
        if valid_ari.any():
            axes[1,1].errorbar(self.results['k'][valid_ari],
                              self.results['temporal_ari_mean'][valid_ari],
                              yerr=self.results['temporal_ari_std'][valid_ari],
                              fmt='yo-', linewidth=2, markersize=4, capsize=5)
        axes[1,1].set_title('Temporal ARI Stability\n(Higher is Better)')
        axes[1,1].set_xlabel('Number of Clusters (k)')
        axes[1,1].set_ylabel('Mean ARI')
        axes[1,1].grid(True, alpha=0.3)

        # Plot 7: Relative Improvement over k=2
        if self.relative_results:
            rel_df = pd.DataFrame(self.relative_results)
            axes[1,2].plot(rel_df['k'], rel_df['improvement_score'], 'ko-', linewidth=2, markersize=4)
            axes[1,2].axhline(y=0, color='r', linestyle='--', alpha=0.5, label='k=2 baseline')
            axes[1,2].set_title('Improvement over k=2\n(Higher is Better)')
            axes[1,2].set_xlabel('Number of Clusters (k)')
            axes[1,2].set_ylabel('Relative Improvement')
            axes[1,2].grid(True, alpha=0.3)
            axes[1,2].legend()
        else:
            axes[1,2].text(0.5, 0.5, 'No relative metrics\n(requires reduced data)',
                          ha='center', va='center', transform=axes[1,2].transAxes)
            axes[1,2].set_title('Relative Improvement')

        # Plot 8: Gap Statistic
        valid_gap = ~np.isnan(self.results['gap_statistic'])
        if valid_gap.any():
            axes[1,3].errorbar(self.results['k'][valid_gap],
                              self.results['gap_statistic'][valid_gap],
                              yerr=self.results['gap_std'][valid_gap],
                              fmt='mo-', linewidth=2, markersize=4, capsize=5)
        axes[1,3].set_title('Gap Statistic\n(Higher is Better)')
        axes[1,3].set_xlabel('Number of Clusters (k)')
        axes[1,3].set_ylabel('Gap Statistic')
        axes[1,3].grid(True, alpha=0.3)

        # Plot 9: WCSS (Elbow Method)
        axes[2,0].plot(self.results['k'], self.results['wcss'], 'ro-', linewidth=2, markersize=4)
        axes[2,0].set_title('Within-Cluster Sum of Squares\n(Look for Elbow)')
        axes[2,0].set_xlabel('Number of Clusters (k)')
        axes[2,0].set_ylabel('WCSS')
        axes[2,0].grid(True, alpha=0.3)

        # Plot 10: Frequency Stability
        axes[2,1].plot(self.results['k'], self.results['frequency_stability'], 'ko-', linewidth=2, markersize=4)
        axes[2,1].set_title('Cluster Frequency Stability\n(Lower CV is Better)')
        axes[2,1].set_xlabel('Number of Clusters (k)')
        axes[2,1].set_ylabel('Coefficient of Variation')
        axes[2,1].grid(True, alpha=0.3)

        # Plot 11: Dunn Index
        valid_dunn = ~np.isnan(self.results['dunn_index'])
        if valid_dunn.any():
            axes[2,2].plot(self.results['k'][valid_dunn], self.results['dunn_index'][valid_dunn], 'bo-', linewidth=2, markersize=4)
        axes[2,2].set_title('Dunn Index\n(Higher is Better)')
        axes[2,2].set_xlabel('Number of Clusters (k)')
        axes[2,2].set_ylabel('Dunn Index')
        axes[2,2].grid(True, alpha=0.3)

        # Plot 12: Combined Stability Score
        # Normalize and combine all stability metrics
        stability_components = []

        # Temporal ARI (higher is better, so invert)
        if not self.results['temporal_ari_mean'].isna().all():
            ari_score = self.results['temporal_ari_mean'].fillna(0)
            if ari_score.max() > ari_score.min():
                ari_norm = (ari_score - ari_score.min()) / (ari_score.max() - ari_score.min())
                stability_components.append(ari_norm)

        # Frequency stability (lower CV is better)
        if not self.results['frequency_stability'].isna().all():
            freq_score = self.results['frequency_stability'].fillna(1)
            if freq_score.max() > freq_score.min():
                freq_norm = 1 - (freq_score - freq_score.min()) / (freq_score.max() - freq_score.min())
                stability_components.append(freq_norm)

        # DVI temporal component (higher is better)
        if not self.results['dvi_temporal'].isna().all():
            dvi_score = self.results['dvi_temporal'].fillna(0)
            if dvi_score.max() > dvi_score.min():
                dvi_norm = (dvi_score - dvi_score.min()) / (dvi_score.max() - dvi_score.min())
                stability_components.append(dvi_norm)

        # Plot 12: Explained Variance Ratio
        valid_explained = ~np.isnan(self.results['explained_variance_ratio'])
        if valid_explained.any():
            axes[2,3].plot(self.results['k'][valid_explained], self.results['explained_variance_ratio'][valid_explained], 'go-', linewidth=2, markersize=4)
        axes[2,3].set_title('Explained Variance Ratio\n(Higher is Better)')
        axes[2,3].set_xlabel('Number of Clusters (k)')
        axes[2,3].set_ylabel('Explained Variance Ratio')
        axes[2,3].grid(True, alpha=0.3)

        # Plot 13: Multi-criterion Decision Support
        axes[3,0].axis('off')

        # Find optimal k for different metrics (bias-corrected)
        recommendations = []

        # Penalized Silhouette optimum
        if not self.results['penalized_silhouette'].isna().all():
            pen_sil_opt = self.results.loc[self.results['penalized_silhouette'].idxmax(), 'k']
            recommendations.append(f"Penalized Silhouette: k={pen_sil_opt}")

        # DVI optimum
        if not self.results['dvi_overall'].isna().all():
            dvi_opt = self.results.loc[self.results['dvi_overall'].idxmax(), 'k']
            recommendations.append(f"Dynamic Validity Index: k={dvi_opt}")

        # Dunn Index optimum
        if not self.results['dunn_index'].isna().all():
            dunn_opt = self.results.loc[self.results['dunn_index'].idxmax(), 'k']
            recommendations.append(f"Dunn Index: k={dunn_opt}")

        # Explained Variance optimum
        if not self.results['explained_variance_ratio'].isna().all():
            exp_var_opt = self.results.loc[self.results['explained_variance_ratio'].idxmax(), 'k']
            recommendations.append(f"Explained Variance: k={exp_var_opt}")

        # Relative improvement optimum
        if self.relative_results:
            rel_df = pd.DataFrame(self.relative_results)
            if not rel_df.empty:
                rel_opt = rel_df.loc[rel_df['improvement_score'].idxmax(), 'k']
                recommendations.append(f"Relative Improvement: k={rel_opt}")

        # Temporal stability optimum
        if not self.results['temporal_ari_mean'].isna().all():
            temp_opt = self.results.loc[self.results['temporal_ari_mean'].idxmax(), 'k']
            recommendations.append(f"Temporal ARI: k={temp_opt}")

        # Seasonal stability optimum
        if not self.results['seasonal_stability_mean'].isna().all():
            seasonal_opt = self.results.loc[self.results['seasonal_stability_mean'].idxmax(), 'k']
            recommendations.append(f"Seasonal Stability: k={seasonal_opt}")

        # Persistence optimum
        if not self.results['overall_persistence'].isna().all():
            persist_opt = self.results.loc[self.results['overall_persistence'].idxmax(), 'k']
            recommendations.append(f"Overall Persistence: k={persist_opt}")

        # Procrustes stability optimum (lower is better)
        if not self.results['procrustes_mean'].isna().all():
            proc_opt = self.results.loc[self.results['procrustes_mean'].idxmin(), 'k']
            recommendations.append(f"Procrustes Stability: k={proc_opt}")

        axes[3,0].text(0.05, 0.95, 'Bias-Corrected Optimal k:', fontsize=14, fontweight='bold',
                      transform=axes[3,0].transAxes)

        for i, rec in enumerate(recommendations[:7]):
            axes[3,0].text(0.05, 0.85 - i*0.1, rec, fontsize=11,
                          transform=axes[3,0].transAxes)

        # Plot 14: Stability Evidence Summary
        axes[3,1].axis('off')

        stability_evidence = []

        # High ARI indicates stable cluster assignments
        if not self.results['temporal_ari_mean'].isna().all():
            max_ari = self.results['temporal_ari_mean'].max()
            max_ari_k = self.results.loc[self.results['temporal_ari_mean'].idxmax(), 'k']
            stability_evidence.append(f"Max ARI: {max_ari:.3f} at k={max_ari_k}")

        # Low frequency CV indicates stable occurrence
        if not self.results['frequency_stability'].isna().all():
            min_freq_cv = self.results['frequency_stability'].min()
            min_freq_k = self.results.loc[self.results['frequency_stability'].idxmin(), 'k']
            stability_evidence.append(f"Min Freq CV: {min_freq_cv:.3f} at k={min_freq_k}")

        # DVI temporal consistency
        if not self.results['dvi_temporal'].isna().all():
            max_dvi_temp = self.results['dvi_temporal'].max()
            max_dvi_temp_k = self.results.loc[self.results['dvi_temporal'].idxmax(), 'k']
            stability_evidence.append(f"Max DVI temporal: {max_dvi_temp:.3f} at k={max_dvi_temp_k}")

        axes[3,1].text(0.05, 0.95, 'Temporal Stability Evidence:', fontsize=14, fontweight='bold',
                      transform=axes[3,1].transAxes)

        for i, evidence in enumerate(stability_evidence[:5]):
            axes[3,1].text(0.05, 0.8 - i*0.12, evidence, fontsize=11,
                          transform=axes[3,1].transAxes)

        # Plot 15: Decision Matrix Heatmap
        decision_metrics = ['penalized_silhouette', 'dvi_overall', 'dunn_index', 'explained_variance_ratio',
                           'temporal_ari_mean', 'seasonal_stability_mean', 'overall_persistence', 'procrustes_mean']
        available_metrics = [m for m in decision_metrics if m in self.results.columns and not self.results[m].isna().all()]

        if available_metrics:
            decision_data = self.results[['k'] + available_metrics].dropna()
            if not decision_data.empty:
                # Normalize metrics (handle direction: higher/lower is better)
                normalized_data = decision_data.copy()
                for metric in available_metrics:
                    values = decision_data[metric].values
                    if metric == 'procrustes_mean':  # Lower is better
                        normalized_data[metric] = 1 - (values - values.min()) / (values.max() - values.min() + 1e-10)
                    else:  # Higher is better
                        normalized_data[metric] = (values - values.min()) / (values.max() - values.min() + 1e-10)

                # Create heatmap
                heatmap_data = normalized_data.set_index('k')[available_metrics].T
                im = axes[3,2].imshow(heatmap_data.values, cmap='RdYlGn', aspect='auto')
                axes[3,2].set_xticks(range(len(heatmap_data.columns)))
                axes[3,2].set_xticklabels(heatmap_data.columns)
                axes[3,2].set_yticks(range(len(available_metrics)))
                axes[3,2].set_yticklabels([m.replace('_', ' ').title() for m in available_metrics])
                axes[3,2].set_title('Decision Matrix\n(Green=Better)')
                plt.colorbar(im, ax=axes[3,2], shrink=0.8)
        else:
            axes[3,2].text(0.5, 0.5, 'Decision matrix requires\ndistance-based metrics',
                          ha='center', va='center', transform=axes[3,2].transAxes)
            axes[3,2].set_title('Decision Matrix')

        # Plot 16: Consensus Recommendation
        axes[3,3].axis('off')

        if available_metrics and not decision_data.empty:
            # Calculate consensus score (average of normalized metrics)
            consensus_scores = normalized_data.set_index('k')[available_metrics].mean(axis=1)
            best_k = consensus_scores.idxmax()
            best_score = consensus_scores.max()

            # Get top 3 recommendations
            top_3 = consensus_scores.nlargest(3)

            axes[3,3].text(0.05, 0.9, 'CONSENSUS RECOMMENDATION:', fontsize=14, fontweight='bold',
                          color='red', transform=axes[3,3].transAxes)
            axes[3,3].text(0.05, 0.75, f'Optimal k = {best_k}', fontsize=16, fontweight='bold',
                          color='darkgreen', transform=axes[3,3].transAxes)
            axes[3,3].text(0.05, 0.6, f'Consensus Score: {best_score:.3f}', fontsize=12,
                          transform=axes[3,3].transAxes)

            axes[3,3].text(0.05, 0.4, 'Top 3 Candidates:', fontsize=12, fontweight='bold',
                          transform=axes[3,3].transAxes)

            for i, (k, score) in enumerate(top_3.items()):
                axes[3,3].text(0.05, 0.3 - i*0.08, f'{i+1}. k={k} (score: {score:.3f})',
                              fontsize=11, transform=axes[3,3].transAxes)
        else:
            axes[3,3].text(0.5, 0.5, 'Consensus requires\nmultiple metrics',
                          ha='center', va='center', transform=axes[3,3].transAxes)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")

        plt.show()

    def calculate_dynamic_validity_index(self, labels, data, window_size=90):
        """
        Calculate Dynamic Validity Index (DVI) for temporal clustering
        Measures clustering quality across time periods and structural changes
        """
        if data is None:
            return np.nan, np.nan, np.nan

        n_samples = len(labels)
        if n_samples < window_size * 3:
            return np.nan, np.nan, np.nan

        # Divide into time periods
        n_periods = n_samples // window_size
        period_scores = []
        transition_scores = []

        unique_labels = np.unique(labels[labels >= 0])
        if len(unique_labels) < 2:
            return np.nan, np.nan, np.nan

        for i in range(n_periods):
            start_idx = i * window_size
            end_idx = min((i + 1) * window_size, n_samples)

            period_labels = labels[start_idx:end_idx]
            period_data = data[start_idx:end_idx]

            # Calculate within-period clustering quality
            try:
                if len(np.unique(period_labels[period_labels >= 0])) > 1:
                    sil_score = silhouette_score(period_data, period_labels)
                    period_scores.append(sil_score)
            except:
                continue

        # Calculate transition quality between periods
        for i in range(n_periods - 1):
            start1 = i * window_size
            end1 = (i + 1) * window_size
            start2 = (i + 1) * window_size
            end2 = min((i + 2) * window_size, n_samples)

            if end2 - start2 < window_size // 2:
                continue

            labels1 = labels[start1:end1]
            labels2 = labels[start2:end2]

            # Measure label consistency between periods
            try:
                # Calculate cluster frequency similarity
                freq1 = np.bincount(labels1[labels1 >= 0], minlength=len(unique_labels))
                freq2 = np.bincount(labels2[labels2 >= 0], minlength=len(unique_labels))

                if np.sum(freq1) > 0 and np.sum(freq2) > 0:
                    freq1_norm = freq1 / np.sum(freq1)
                    freq2_norm = freq2 / np.sum(freq2)

                    # Jensen-Shannon divergence as transition score
                    m = (freq1_norm + freq2_norm) / 2
                    js_div = 0.5 * np.sum(freq1_norm * np.log(freq1_norm / (m + 1e-10) + 1e-10)) + \
                            0.5 * np.sum(freq2_norm * np.log(freq2_norm / (m + 1e-10) + 1e-10))

                    transition_scores.append(1 - js_div)  # Convert to similarity
            except:
                continue

        # Calculate overall DVI components
        individual_quality = np.mean(period_scores) if period_scores else np.nan
        temporal_consistency = np.mean(transition_scores) if transition_scores else np.nan

        # Overall DVI (harmonic mean of components)
        if not np.isnan(individual_quality) and not np.isnan(temporal_consistency):
            overall_dvi = 2 * (individual_quality * temporal_consistency) / (individual_quality + temporal_consistency + 1e-10)
        else:
            overall_dvi = np.nan

        return overall_dvi, individual_quality, temporal_consistency

    def calculate_relative_metrics(self, k_range, data=None):
        """Calculate metrics relative to k=2 baseline to reduce bias"""
        if data is None:
            return {}

        # Get k=2 baseline scores
        baseline_labels = self.ds['k=2'].values
        try:
            baseline_sil = silhouette_score(data, baseline_labels)
            baseline_ch = calinski_harabasz_score(data, baseline_labels)
            baseline_db = davies_bouldin_score(data, baseline_labels)
        except:
            return {}

        relative_metrics = {
            'k': [],
            'relative_silhouette': [],
            'relative_ch': [],
            'relative_db': [],
            'improvement_score': []
        }

        for k in k_range:
            if k == 2:
                continue

            labels = self.ds[f'k={k}'].values
            try:
                sil_score = silhouette_score(data, labels)
                ch_score = calinski_harabasz_score(data, labels)
                db_score = davies_bouldin_score(data, labels)

                # Calculate relative improvements
                rel_sil = (sil_score - baseline_sil) / abs(baseline_sil + 1e-10)
                rel_ch = (ch_score - baseline_ch) / (baseline_ch + 1e-10)
                rel_db = (baseline_db - db_score) / (baseline_db + 1e-10)  # Lower is better for DB

                # Combined improvement score
                improvement = (rel_sil + rel_ch + rel_db) / 3

                relative_metrics['k'].append(k)
                relative_metrics['relative_silhouette'].append(rel_sil)
                relative_metrics['relative_ch'].append(rel_ch)
                relative_metrics['relative_db'].append(rel_db)
                relative_metrics['improvement_score'].append(improvement)

            except:
                continue

        return relative_metrics

    def calculate_temporal_stability(self, labels, window_size=30):
        """Calculate temporal stability using moving windows"""
        if len(labels) < window_size * 2:
            return np.nan, np.nan

        ari_scores = []
        n_windows = len(labels) - window_size + 1

        for i in range(0, n_windows - window_size, window_size//2):
            window1 = labels[i:i+window_size]
            window2 = labels[i+window_size:i+2*window_size]

            if len(window2) == window_size:
                ari = adjusted_rand_score(window1, window2)
                ari_scores.append(ari)

        if ari_scores:
            return np.mean(ari_scores), np.std(ari_scores)
        return np.nan, np.nan

    def calculate_cluster_frequency_stability(self, labels, n_periods=4):
        """Calculate stability of cluster frequencies across time periods"""
        n_samples = len(labels)
        period_size = n_samples // n_periods

        if period_size < 10:  # Not enough data
            return np.nan

        frequencies = []
        unique_clusters = np.unique(labels[labels >= 0])

        for i in range(n_periods):
            start_idx = i * period_size
            end_idx = (i + 1) * period_size if i < n_periods - 1 else n_samples
            period_labels = labels[start_idx:end_idx]

            period_freq = []
            for cluster in unique_clusters:
                freq = np.sum(period_labels == cluster) / len(period_labels)
                period_freq.append(freq)
            frequencies.append(period_freq)

        # Calculate coefficient of variation for each cluster
        frequencies = np.array(frequencies)
        cv_scores = []

        for j in range(len(unique_clusters)):
            cluster_freqs = frequencies[:, j]
            if np.mean(cluster_freqs) > 0:
                cv = np.std(cluster_freqs) / np.mean(cluster_freqs)
                cv_scores.append(cv)

        return np.mean(cv_scores) if cv_scores else np.nan
    
    def calculate_all_metrics(self, k_range=range(2, 36), dates=None):
        """Calculate all clustering validation metrics using reduced data"""

        metrics = {
            'k': [],
            'silhouette': [],
            'calinski_harabasz': [],
            'davies_bouldin': [],
            'dunn_index': [],
            'penalized_silhouette': [],
            'penalized_ch': [],
            'penalized_db': [],
            'complexity_penalty': [],
            'wcss': [],
            'gap_statistic': [],
            'gap_std': [],
            'temporal_ari_mean': [],
            'temporal_ari_std': [],
            'frequency_stability': [],
            'seasonal_stability_mean': [],
            'seasonal_stability_std': [],
            'overall_persistence': [],
            'explained_variance_ratio': [],
            'dvi_overall': [],
            'dvi_individual': [],
            'dvi_temporal': [],
            'procrustes_mean': [],
            'procrustes_std': []
        }
        
        print("Calculating clustering validation metrics...")
        
        # Check if we have reduced data
        if self.reduced_data is None:
            print("Warning: No reduced data available. Distance-based metrics will be NaN.")
            print("Use setup_with_clusterer() or provide reduced_data in __init__")
        
        # Collect all labels for Procrustes analysis
        all_labels = []
        for k in k_range:
            if f'k={k}' in self.ds:
                all_labels.append(self.ds[f'k={k}'].values)
        
        # Calculate Procrustes stability once for all k
        if self.reduced_data is not None and all_labels:
            procrustes_mean, procrustes_std = self.calculate_procrustes_stability(all_labels, self.reduced_data)
        else:
            procrustes_mean, procrustes_std = np.nan, np.nan
        
        for k in k_range:
            print(f"Processing k={k}...")
            
            # Get cluster labels
            labels = self.ds[f'k={k}'].values
            
            # Basic metrics
            metrics['k'].append(k)
            
            # Distance-based metrics (require reduced data)
            if self.reduced_data is not None:
                try:
                    # Ensure we have the right number of samples
                    if len(labels) == self.reduced_data.shape[0]:
                        sil_score = silhouette_score(self.reduced_data, labels)
                        ch_score = calinski_harabasz_score(self.reduced_data, labels)
                        db_score = davies_bouldin_score(self.reduced_data, labels)
                        dunn_score = self.calculate_dunn_index(labels, self.reduced_data)
                        wcss = self.calculate_wcss(labels, self.reduced_data)
                        gap, gap_std = self.calculate_gap_statistic(labels, self.reduced_data)

                        # Penalized metrics to address 2-cluster bias
                        pen_sil, pen_ch, pen_db, complexity = self.calculate_penalized_metrics(
                            labels, self.reduced_data, k)

                        # Dynamic Validity Index
                        dvi_overall, dvi_individual, dvi_temporal = self.calculate_dynamic_validity_index(
                            labels, self.reduced_data)

                        # Explained variance
                        wt_variances, explained_var_ratio = self.calculate_explained_variance_per_wt(
                            labels, self.reduced_data)

                    else:
                        print(f"Warning: Dimension mismatch for k={k}. Labels: {len(labels)}, Data: {self.reduced_data.shape[0]}")
                        sil_score = ch_score = db_score = dunn_score = wcss = gap = gap_std = np.nan
                        pen_sil = pen_ch = pen_db = complexity = np.nan
                        dvi_overall = dvi_individual = dvi_temporal = np.nan
                        explained_var_ratio = np.nan

                except Exception as e:
                    print(f"Error calculating distance-based metrics for k={k}: {e}")
                    sil_score = ch_score = db_score = dunn_score = wcss = gap = gap_std = np.nan
                    pen_sil = pen_ch = pen_db = complexity = np.nan
                    dvi_overall = dvi_individual = dvi_temporal = np.nan
                    explained_var_ratio = np.nan
            else:
                sil_score = ch_score = db_score = dunn_score = wcss = gap = gap_std = np.nan
                pen_sil = pen_ch = pen_db = complexity = np.nan
                dvi_overall = dvi_individual = dvi_temporal = np.nan
                explained_var_ratio = np.nan

            # Temporal stability metrics (work without reduced data)
            try:
                ari_mean, ari_std = self.calculate_temporal_stability(labels)
            except Exception as e:
                print(f"Warning: Error calculating temporal stability for k={k}: {e}")
                ari_mean, ari_std = np.nan, np.nan

            try:
                freq_stability = self.calculate_cluster_frequency_stability(labels)
            except Exception as e:
                print(f"Warning: Error calculating frequency stability for k={k}: {e}")
                freq_stability = np.nan

            try:
                seasonal_mean, seasonal_std = self.calculate_seasonal_stability(labels, dates)
            except Exception as e:
                print(f"Warning: Error calculating seasonal stability for k={k}: {e}")
                seasonal_mean, seasonal_std = np.nan, np.nan

            try:
                persistence_metrics, overall_persistence = self.calculate_persistence_metrics(labels)
            except Exception as e:
                print(f"Warning: Error calculating persistence metrics for k={k}: {e}")
                persistence_metrics, overall_persistence = {}, np.nan

            metrics['silhouette'].append(sil_score)
            metrics['calinski_harabasz'].append(ch_score)
            metrics['davies_bouldin'].append(db_score)
            metrics['dunn_index'].append(dunn_score)
            metrics['penalized_silhouette'].append(pen_sil)
            metrics['penalized_ch'].append(pen_ch)
            metrics['penalized_db'].append(pen_db)
            metrics['complexity_penalty'].append(complexity)
            metrics['wcss'].append(wcss)
            metrics['gap_statistic'].append(gap)
            metrics['gap_std'].append(gap_std)
            metrics['temporal_ari_mean'].append(ari_mean)
            metrics['temporal_ari_std'].append(ari_std)
            metrics['frequency_stability'].append(freq_stability)
            metrics['seasonal_stability_mean'].append(seasonal_mean)
            metrics['seasonal_stability_std'].append(seasonal_std)
            metrics['overall_persistence'].append(overall_persistence)
            metrics['explained_variance_ratio'].append(explained_var_ratio)
            metrics['dvi_overall'].append(dvi_overall)
            metrics['dvi_individual'].append(dvi_individual)
            metrics['dvi_temporal'].append(dvi_temporal)
            metrics['procrustes_mean'].append(procrustes_mean)
            metrics['procrustes_std'].append(procrustes_std)

            # Store detailed WT variance info for this k
            if k not in hasattr(self, 'wt_variance_details'):
                if not hasattr(self, 'wt_variance_details'):
                    self.wt_variance_details = {}
            if self.reduced_data is not None and len(labels) == self.reduced_data.shape[0]:
                wt_variances, _ = self.calculate_explained_variance_per_wt(labels, self.reduced_data)
                self.wt_variance_details[k] = wt_variances
        
        self.results = pd.DataFrame(metrics)
        
        # Calculate relative metrics to address 2-cluster bias
        if self.reduced_data is not None:
            self.relative_results = self.calculate_relative_metrics(k_range, self.reduced_data)
        else:
            self.relative_results = {}
        
        return self.results

    def get_wt_detailed_analysis(self, k):
        """Get detailed analysis for a specific k value"""
        if not hasattr(self, 'wt_variance_details') or k not in self.wt_variance_details:
            print(f"No detailed analysis available for k={k}. Run calculate_all_metrics() first.")
            return None

        analysis = {
            'k': k,
            'weather_types': self.wt_variance_details[k],
            'summary': {}
        }

        # Calculate summary statistics
        if self.wt_variance_details[k]:
            sizes = [wt['size'] for wt in self.wt_variance_details[k].values()]
            explained_vars = [wt['explained_variance_ratio'] for wt in self.wt_variance_details[k].values()]

            analysis['summary'] = {
                'total_wts': len(self.wt_variance_details[k]),
                'mean_size': np.mean(sizes),
                'size_std': np.std(sizes),
                'size_cv': np.std(sizes) / np.mean(sizes) if np.mean(sizes) > 0 else np.nan,
                'mean_explained_var': np.mean(explained_vars),
                'min_explained_var': np.min(explained_vars),
                'max_explained_var': np.max(explained_vars)
            }

        return analysis

    def print_wt_summary(self, k):
        """Print a detailed summary for a specific k value"""
        analysis = self.get_wt_detailed_analysis(k)
        if analysis is None:
            return

        print(f"\n{'='*60}")
        print(f"WEATHER TYPE ANALYSIS FOR k={k}")
        print(f"{'='*60}")

        print(f"\nSUMMARY STATISTICS:")
        print(f"  Total Weather Types: {analysis['summary']['total_wts']}")
        print(f"  Mean WT Size: {analysis['summary']['mean_size']:.1f} days")
        print(f"  Size Variability (CV): {analysis['summary']['size_cv']:.3f}")
        print(f"  Mean Explained Variance: {analysis['summary']['mean_explained_var']:.3f}")
        print(f"  Explained Variance Range: {analysis['summary']['min_explained_var']:.3f} - {analysis['summary']['max_explained_var']:.3f}")

        print(f"\nINDIVIDUAL WEATHER TYPES:")
        print(f"{'WT':<4} {'Size':<6} {'Freq':<6} {'ExplVar':<8} {'WithinVar':<10}")
        print("-" * 40)

        for wt_name, wt_data in analysis['weather_types'].items():
            freq = wt_data['size'] / sum(wt['size'] for wt in analysis['weather_types'].values())
            print(f"{wt_name:<4} {wt_data['size']:<6} {freq:<6.3f} {wt_data['explained_variance_ratio']:<8.3f} {wt_data['within_variance']:<10.2f}")

        print(f"\n{'='*60}")

    def plot_wt_characteristics(self, k_values=None, figsize=(15, 10)):
        """Plot weather type characteristics across different k values"""
        if not hasattr(self, 'wt_variance_details'):
            print("No WT details available. Run calculate_all_metrics() first.")
            return

        if k_values is None:
            k_values = sorted(self.wt_variance_details.keys())

        fig, axes = plt.subplots(2, 3, figsize=figsize)
        fig.suptitle('Weather Type Characteristics Analysis', fontsize=16, fontweight='bold')

        # Collect data for plotting
        k_list = []
        mean_sizes = []
        size_cvs = []
        mean_explained_vars = []
        min_explained_vars = []
        max_explained_vars = []
        n_wts = []

        for k in k_values:
            if k in self.wt_variance_details:
                analysis = self.get_wt_detailed_analysis(k)
                if analysis and analysis['summary']:
                    k_list.append(k)
                    mean_sizes.append(analysis['summary']['mean_size'])
                    size_cvs.append(analysis['summary']['size_cv'])
                    mean_explained_vars.append(analysis['summary']['mean_explained_var'])
                    min_explained_vars.append(analysis['summary']['min_explained_var'])
                    max_explained_vars.append(analysis['summary']['max_explained_var'])
                    n_wts.append(analysis['summary']['total_wts'])

        if not k_list:
            print("No valid data for plotting.")
            return

        # Plot 1: Mean WT Size
        axes[0,0].plot(k_list, mean_sizes, 'bo-', linewidth=2, markersize=4)
        axes[0,0].set_title('Mean Weather Type Size')
        axes[0,0].set_xlabel('Number of Clusters (k)')
        axes[0,0].set_ylabel('Mean Size (days)')
        axes[0,0].grid(True, alpha=0.3)

        # Plot 2: Size Variability
        axes[0,1].plot(k_list, size_cvs, 'ro-', linewidth=2, markersize=4)
        axes[0,1].set_title('WT Size Variability (CV)')
        axes[0,1].set_xlabel('Number of Clusters (k)')
        axes[0,1].set_ylabel('Coefficient of Variation')
        axes[0,1].grid(True, alpha=0.3)

        # Plot 3: Explained Variance
        axes[0,2].plot(k_list, mean_explained_vars, 'go-', linewidth=2, markersize=4, label='Mean')
        axes[0,2].fill_between(k_list, min_explained_vars, max_explained_vars, alpha=0.3, color='green')
        axes[0,2].set_title('Explained Variance per WT')
        axes[0,2].set_xlabel('Number of Clusters (k)')
        axes[0,2].set_ylabel('Explained Variance Ratio')
        axes[0,2].grid(True, alpha=0.3)
        axes[0,2].legend()

        # Plot 4: Number of WTs (should equal k)
        axes[1,0].plot(k_list, n_wts, 'mo-', linewidth=2, markersize=4)
        axes[1,0].plot(k_list, k_list, 'k--', alpha=0.5, label='Expected')
        axes[1,0].set_title('Number of Weather Types')
        axes[1,0].set_xlabel('Number of Clusters (k)')
        axes[1,0].set_ylabel('Actual Number of WTs')
        axes[1,0].grid(True, alpha=0.3)
        axes[1,0].legend()

        # Plot 5: Size vs Explained Variance scatter for selected k
        if k_list:
            selected_k = k_list[len(k_list)//2]  # Middle k value
            analysis = self.get_wt_detailed_analysis(selected_k)
            if analysis:
                sizes = [wt['size'] for wt in analysis['weather_types'].values()]
                explained_vars = [wt['explained_variance_ratio'] for wt in analysis['weather_types'].values()]

                axes[1,1].scatter(sizes, explained_vars, alpha=0.7, s=50)
                axes[1,1].set_title(f'Size vs Explained Variance (k={selected_k})')
                axes[1,1].set_xlabel('WT Size (days)')
                axes[1,1].set_ylabel('Explained Variance Ratio')
                axes[1,1].grid(True, alpha=0.3)

        # Plot 6: Frequency distribution for selected k
        if k_list:
            selected_k = k_list[len(k_list)//2]  # Middle k value
            analysis = self.get_wt_detailed_analysis(selected_k)
            if analysis:
                sizes = [wt['size'] for wt in analysis['weather_types'].values()]
                total_size = sum(sizes)
                frequencies = [size/total_size for size in sizes]

                axes[1,2].bar(range(len(frequencies)), frequencies, alpha=0.7)
                axes[1,2].set_title(f'WT Frequency Distribution (k={selected_k})')
                axes[1,2].set_xlabel('Weather Type Index')
                axes[1,2].set_ylabel('Frequency')
                axes[1,2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def generate_comprehensive_report(self, output_file=None):
        """Generate a comprehensive validation report"""
        if self.results.empty:
            print("No results available. Run calculate_all_metrics() first.")
            return

        report = []
        report.append("="*80)
        report.append("COMPREHENSIVE WEATHER TYPING CLUSTER VALIDATION REPORT")
        report.append("="*80)
        report.append("")

        # Summary of metrics calculated
        report.append("METRICS CALCULATED:")
        report.append("-" * 20)
        available_metrics = [col for col in self.results.columns if col != 'k' and not self.results[col].isna().all()]
        for metric in available_metrics:
            report.append(f"  ✓ {metric.replace('_', ' ').title()}")
        report.append("")

        # Optimal k recommendations
        report.append("OPTIMAL k RECOMMENDATIONS:")
        report.append("-" * 30)

        # Get optimal k for each metric
        recommendations = {}

        # Distance-based metrics (higher is better)
        for metric in ['penalized_silhouette', 'calinski_harabasz', 'dunn_index', 'explained_variance_ratio', 'dvi_overall']:
            if metric in self.results.columns and not self.results[metric].isna().all():
                opt_k = self.results.loc[self.results[metric].idxmax(), 'k']
                recommendations[metric] = opt_k

        # Stability metrics (higher is better)
        for metric in ['temporal_ari_mean', 'seasonal_stability_mean', 'overall_persistence']:
            if metric in self.results.columns and not self.results[metric].isna().all():
                opt_k = self.results.loc[self.results[metric].idxmax(), 'k']
                recommendations[metric] = opt_k

        # Lower is better metrics
        for metric in ['davies_bouldin', 'frequency_stability', 'procrustes_mean']:
            if metric in self.results.columns and not self.results[metric].isna().all():
                opt_k = self.results.loc[self.results[metric].idxmin(), 'k']
                recommendations[metric] = opt_k

        # Count votes for each k
        k_votes = {}
        for metric, k in recommendations.items():
            if k not in k_votes:
                k_votes[k] = []
            k_votes[k].append(metric)

        # Sort by number of votes
        sorted_k = sorted(k_votes.items(), key=lambda x: len(x[1]), reverse=True)

        report.append("Consensus Ranking:")
        for i, (k, supporting_metrics) in enumerate(sorted_k[:5]):  # Top 5
            report.append(f"  {i+1}. k={k} ({len(supporting_metrics)} votes)")
            for metric in supporting_metrics:
                report.append(f"     - {metric.replace('_', ' ').title()}")
        report.append("")

        # Stability analysis
        report.append("TEMPORAL STABILITY ANALYSIS:")
        report.append("-" * 35)

        if not self.results['temporal_ari_mean'].isna().all():
            best_ari_k = self.results.loc[self.results['temporal_ari_mean'].idxmax(), 'k']
            best_ari_val = self.results['temporal_ari_mean'].max()
            report.append(f"Best Temporal ARI: k={best_ari_k} (ARI={best_ari_val:.3f})")

        if not self.results['frequency_stability'].isna().all():
            best_freq_k = self.results.loc[self.results['frequency_stability'].idxmin(), 'k']
            best_freq_val = self.results['frequency_stability'].min()
            report.append(f"Most Stable Frequencies: k={best_freq_k} (CV={best_freq_val:.3f})")

        if not self.results['seasonal_stability_mean'].isna().all():
            best_seasonal_k = self.results.loc[self.results['seasonal_stability_mean'].idxmax(), 'k']
            best_seasonal_val = self.results['seasonal_stability_mean'].max()
            report.append(f"Best Seasonal Stability: k={best_seasonal_k} (ARI={best_seasonal_val:.3f})")

        if not self.results['overall_persistence'].isna().all():
            best_persist_k = self.results.loc[self.results['overall_persistence'].idxmax(), 'k']
            best_persist_val = self.results['overall_persistence'].max()
            report.append(f"Highest Persistence: k={best_persist_k} (Persistence={best_persist_val:.3f})")

        report.append("")

        # Quality metrics summary
        report.append("CLUSTERING QUALITY SUMMARY:")
        report.append("-" * 30)

        if not self.results['explained_variance_ratio'].isna().all():
            best_var_k = self.results.loc[self.results['explained_variance_ratio'].idxmax(), 'k']
            best_var_val = self.results['explained_variance_ratio'].max()
            report.append(f"Best Explained Variance: k={best_var_k} ({best_var_val:.3f})")

        if not self.results['penalized_silhouette'].isna().all():
            best_sil_k = self.results.loc[self.results['penalized_silhouette'].idxmax(), 'k']
            best_sil_val = self.results['penalized_silhouette'].max()
            report.append(f"Best Penalized Silhouette: k={best_sil_k} ({best_sil_val:.3f})")

        if not self.results['dvi_overall'].isna().all():
            best_dvi_k = self.results.loc[self.results['dvi_overall'].idxmax(), 'k']
            best_dvi_val = self.results['dvi_overall'].max()
            report.append(f"Best Dynamic Validity Index: k={best_dvi_k} ({best_dvi_val:.3f})")

        report.append("")

        # Final recommendation
        if sorted_k:
            final_k = sorted_k[0][0]
            report.append("FINAL RECOMMENDATION:")
            report.append("-" * 25)
            report.append(f"Based on consensus across multiple metrics: k = {final_k}")
            report.append(f"This choice is supported by {len(sorted_k[0][1])} different validation metrics.")
            report.append("")

            # Show detailed analysis for recommended k
            if hasattr(self, 'wt_variance_details') and final_k in self.wt_variance_details:
                analysis = self.get_wt_detailed_analysis(final_k)
                if analysis:
                    report.append(f"DETAILED ANALYSIS FOR k={final_k}:")
                    report.append("-" * 30)
                    report.append(f"Number of Weather Types: {analysis['summary']['total_wts']}")
                    report.append(f"Mean WT Size: {analysis['summary']['mean_size']:.1f} days")
                    report.append(f"Size Variability (CV): {analysis['summary']['size_cv']:.3f}")
                    report.append(f"Mean Explained Variance: {analysis['summary']['mean_explained_var']:.3f}")

        report.append("")
        report.append("="*80)

        # Join report and print/save
        report_text = "\n".join(report)
        print(report_text)

        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_text)
            print(f"\nReport saved to {output_file}")

        return report_text
            
    def calculate_gap_statistic(self, labels, data, n_refs=10):
        """Calculate Gap Statistic with proper standard error"""
        if data is None:
            return np.nan, np.nan

        # Calculate WCSS for actual data
        actual_wcss = self.calculate_wcss(labels, data)
        if actual_wcss <= 0:
            return np.nan, np.nan

        # Generate reference datasets and calculate WCSS
        ref_wcss = []
        n_samples, n_features = data.shape

        for _ in range(n_refs):
            # Generate uniform random data within the range of actual data
            ref_data = np.random.uniform(
                low=data.min(axis=0),
                high=data.max(axis=0),
                size=(n_samples, n_features)
            )
            # Apply same clustering (simple k-means approximation)
            n_clusters = len(np.unique(labels[labels >= 0]))
            if n_clusters > 1:
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                ref_labels = kmeans.fit_predict(ref_data)
                ref_wcss_val = self.calculate_wcss(ref_labels, ref_data)
                if ref_wcss_val > 0:
                    ref_wcss.append(ref_wcss_val)

        if ref_wcss:
            # Proper gap statistic calculation
            log_ref_wcss = np.log(ref_wcss)
            gap = np.mean(log_ref_wcss) - np.log(actual_wcss)
            # Standard error calculation
            gap_se = np.sqrt(np.var(log_ref_wcss)) * np.sqrt(1 + 1/n_refs)
            return gap, gap_se
        return np.nan, np.nan

    def calculate_dunn_index(self, labels, data):
        """Calculate Dunn Index - ratio of minimum inter-cluster to maximum intra-cluster distance"""
        if data is None:
            return np.nan

        unique_labels = np.unique(labels[labels >= 0])
        if len(unique_labels) < 2:
            return np.nan

        # Calculate intra-cluster distances (maximum)
        max_intra_dist = 0
        for label in unique_labels:
            cluster_points = data[labels == label]
            if len(cluster_points) > 1:
                intra_dists = pdist(cluster_points)
                if len(intra_dists) > 0:
                    max_intra_dist = max(max_intra_dist, np.max(intra_dists))

        # Calculate inter-cluster distances (minimum)
        min_inter_dist = np.inf
        for i, label1 in enumerate(unique_labels):
            for label2 in unique_labels[i+1:]:
                cluster1 = data[labels == label1]
                cluster2 = data[labels == label2]

                # Calculate minimum distance between clusters
                for point1 in cluster1:
                    for point2 in cluster2:
                        dist = np.linalg.norm(point1 - point2)
                        min_inter_dist = min(min_inter_dist, dist)

        if max_intra_dist > 0 and min_inter_dist < np.inf:
            return min_inter_dist / max_intra_dist
        return np.nan

    def calculate_explained_variance_per_wt(self, labels, data):
        """Calculate explained variance for each weather type"""
        if data is None:
            return {}, np.nan

        unique_labels = np.unique(labels[labels >= 0])
        if len(unique_labels) < 2:
            return {}, np.nan

        # Total variance
        total_var = np.var(data, axis=0).sum()

        # Within-cluster variance for each WT
        wt_variances = {}
        total_within_var = 0

        for label in unique_labels:
            cluster_points = data[labels == label]
            if len(cluster_points) > 1:
                cluster_var = np.var(cluster_points, axis=0).sum()
                wt_variances[f'WT_{label}'] = {
                    'within_variance': cluster_var,
                    'explained_variance': max(0, total_var - cluster_var),
                    'explained_variance_ratio': max(0, (total_var - cluster_var) / total_var) if total_var > 0 else 0,
                    'size': len(cluster_points)
                }
                total_within_var += cluster_var * len(cluster_points)
            else:
                wt_variances[f'WT_{label}'] = {
                    'within_variance': 0,
                    'explained_variance': 0,
                    'explained_variance_ratio': 0,
                    'size': len(cluster_points)
                }

        # Overall explained variance ratio
        total_explained_var_ratio = max(0, (total_var - total_within_var/len(data)) / total_var) if total_var > 0 else 0

        return wt_variances, total_explained_var_ratio

    def calculate_seasonal_stability(self, labels, dates=None, n_seasons=4):
        """Calculate stability across seasons"""
        n_samples = len(labels)

        if dates is None:
            # Assume daily data and create artificial seasons
            # Ensure season_labels has exactly the same length as labels
            season_labels = np.array([i % n_seasons for i in range(n_samples)])
        else:
            # Use actual dates to determine seasons
            if hasattr(dates, '__len__') and len(dates) == n_samples:
                # Proper date-based seasonal assignment would go here
                # For now, use simple modulo approach
                season_labels = np.array([i % n_seasons for i in range(n_samples)])
            else:
                # Fallback to simple approach
                season_labels = np.array([i % n_seasons for i in range(n_samples)])

        # Ensure arrays have the same length
        if len(season_labels) != n_samples:
            season_labels = np.array([i % n_seasons for i in range(n_samples)])

        # Calculate ARI between seasons
        seasonal_aris = []
        unique_seasons = np.unique(season_labels)

        for i, season1 in enumerate(unique_seasons):
            for season2 in unique_seasons[i+1:]:
                mask1 = season_labels == season1
                mask2 = season_labels == season2

                if np.sum(mask1) > 0 and np.sum(mask2) > 0:
                    labels1 = labels[mask1]
                    labels2 = labels[mask2]

                    # Sample equal sizes if needed
                    min_size = min(len(labels1), len(labels2))
                    if min_size > 10:  # Minimum sample size
                        try:
                            labels1_sample = np.random.choice(labels1, min_size, replace=False)
                            labels2_sample = np.random.choice(labels2, min_size, replace=False)
                            ari = adjusted_rand_score(labels1_sample, labels2_sample)
                            seasonal_aris.append(ari)
                        except:
                            # Skip if sampling fails
                            continue

        if seasonal_aris:
            return np.mean(seasonal_aris), np.std(seasonal_aris)
        return np.nan, np.nan

    def calculate_persistence_metrics(self, labels, max_lag=7):
        """Calculate weather type persistence and transition metrics"""
        if len(labels) < max_lag * 2:
            return {}, np.nan

        unique_labels = np.unique(labels[labels >= 0])
        persistence_metrics = {}

        # Calculate persistence for each WT
        for label in unique_labels:
            wt_mask = labels == label
            wt_indices = np.where(wt_mask)[0]

            if len(wt_indices) > 0:
                # Calculate run lengths
                run_lengths = []
                current_run = 1

                for i in range(1, len(labels)):
                    if labels[i] == label and labels[i-1] == label:
                        current_run += 1
                    elif labels[i-1] == label:
                        run_lengths.append(current_run)
                        current_run = 1
                    elif labels[i] == label:
                        current_run = 1

                # Add final run if it ends with this WT
                if labels[-1] == label:
                    run_lengths.append(current_run)

                if run_lengths:
                    persistence_metrics[f'WT_{label}'] = {
                        'mean_persistence': np.mean(run_lengths),
                        'max_persistence': np.max(run_lengths),
                        'persistence_std': np.std(run_lengths),
                        'frequency': np.sum(wt_mask) / len(labels)
                    }

        # Calculate transition matrix
        transition_matrix = np.zeros((len(unique_labels), len(unique_labels)))
        label_to_idx = {label: i for i, label in enumerate(unique_labels)}

        for i in range(len(labels) - 1):
            if labels[i] >= 0 and labels[i+1] >= 0:
                from_idx = label_to_idx[labels[i]]
                to_idx = label_to_idx[labels[i+1]]
                transition_matrix[from_idx, to_idx] += 1

        # Normalize transition matrix
        row_sums = transition_matrix.sum(axis=1)
        transition_matrix = transition_matrix / row_sums[:, np.newaxis]
        transition_matrix = np.nan_to_num(transition_matrix)

        # Calculate overall persistence (diagonal sum)
        overall_persistence = np.trace(transition_matrix) / len(unique_labels)

        return persistence_metrics, overall_persistence
    
    def plot_metrics(self, figsize=(15, 12), save_path=None):
        """Create comprehensive plots of all validation metrics"""
        
        if self.results.empty:
            print("No results to plot. Run calculate_all_metrics() first.")
            return
        
        fig, axes = plt.subplots(3, 3, figsize=figsize)
        fig.suptitle('Weather Typing Cluster Validation Metrics', fontsize=16, fontweight='bold')
        
        # Plot 1: Silhouette Score
        axes[0,0].plot(self.results['k'], self.results['silhouette'], 'bo-', linewidth=2, markersize=4)
        axes[0,0].set_title('Silhouette Score\n(Higher is Better)')
        axes[0,0].set_xlabel('Number of Clusters (k)')
        axes[0,0].set_ylabel('Silhouette Score')
        axes[0,0].grid(True, alpha=0.3)
        
        # Plot 2: Calinski-Harabasz Index
        axes[0,1].plot(self.results['k'], self.results['calinski_harabasz'], 'go-', linewidth=2, markersize=4)
        axes[0,1].set_title('Calinski-Harabasz Index\n(Higher is Better)')
        axes[0,1].set_xlabel('Number of Clusters (k)')
        axes[0,1].set_ylabel('CH Index')
        axes[0,1].grid(True, alpha=0.3)
        
        # Plot 3: Davies-Bouldin Index
        axes[0,2].plot(self.results['k'], self.results['davies_bouldin'], 'ro-', linewidth=2, markersize=4)
        axes[0,2].set_title('Davies-Bouldin Index\n(Lower is Better)')
        axes[0,2].set_xlabel('Number of Clusters (k)')
        axes[0,2].set_ylabel('DB Index')
        axes[0,2].grid(True, alpha=0.3)
        
        # Plot 4: WCSS (Elbow Method)
        axes[1,0].plot(self.results['k'], self.results['wcss'], 'mo-', linewidth=2, markersize=4)
        axes[1,0].set_title('Within-Cluster Sum of Squares\n(Look for Elbow)')
        axes[1,0].set_xlabel('Number of Clusters (k)')
        axes[1,0].set_ylabel('WCSS')
        axes[1,0].grid(True, alpha=0.3)
        
        # Plot 5: Gap Statistic
        valid_gap = ~np.isnan(self.results['gap_statistic'])
        if valid_gap.any():
            axes[1,1].errorbar(self.results['k'][valid_gap], 
                              self.results['gap_statistic'][valid_gap],
                              yerr=self.results['gap_std'][valid_gap], 
                              fmt='co-', linewidth=2, markersize=4, capsize=5)
        axes[1,1].set_title('Gap Statistic\n(Higher is Better)')
        axes[1,1].set_xlabel('Number of Clusters (k)')
        axes[1,1].set_ylabel('Gap Statistic')
        axes[1,1].grid(True, alpha=0.3)
        
        # Plot 6: Temporal ARI Stability
        valid_ari = ~np.isnan(self.results['temporal_ari_mean'])
        if valid_ari.any():
            axes[1,2].errorbar(self.results['k'][valid_ari], 
                              self.results['temporal_ari_mean'][valid_ari],
                              yerr=self.results['temporal_ari_std'][valid_ari], 
                              fmt='yo-', linewidth=2, markersize=4, capsize=5)
        axes[1,2].set_title('Temporal ARI Stability\n(Higher is Better)')
        axes[1,2].set_xlabel('Number of Clusters (k)')
        axes[1,2].set_ylabel('Mean ARI')
        axes[1,2].grid(True, alpha=0.3)
        
        # Plot 7: Frequency Stability
        axes[2,0].plot(self.results['k'], self.results['frequency_stability'], 'ko-', linewidth=2, markersize=4)
        axes[2,0].set_title('Cluster Frequency Stability\n(Lower CV is Better)')
        axes[2,0].set_xlabel('Number of Clusters (k)')
        axes[2,0].set_ylabel('Coefficient of Variation')
        axes[2,0].grid(True, alpha=0.3)
        
        # Plot 8: Combined Stability Score
        # Normalize and combine temporal metrics
        temporal_score = 1 - self.results['temporal_ari_mean'].fillna(0)  # Lower is better
        frequency_score = self.results['frequency_stability'].fillna(1)   # Lower is better
        
        # Normalize to 0-1 range
        if temporal_score.max() > temporal_score.min():
            temporal_score = (temporal_score - temporal_score.min()) / (temporal_score.max() - temporal_score.min())
        if frequency_score.max() > frequency_score.min():
            frequency_score = (frequency_score - frequency_score.min()) / (frequency_score.max() - frequency_score.min())
        
        combined_stability = (temporal_score + frequency_score) / 2
        
        axes[2,1].plot(self.results['k'], combined_stability, 'go-', linewidth=2, markersize=4)
        axes[2,1].set_title('Combined Temporal Stability\n(Lower is Better)')
        axes[2,1].set_xlabel('Number of Clusters (k)')
        axes[2,1].set_ylabel('Stability Score')
        axes[2,1].grid(True, alpha=0.3)
        
        # Plot 9: Optimal k recommendations
        axes[2,2].axis('off')
        
        # Find optimal k for different metrics
        recommendations = []
        
        # Silhouette optimum
        if not self.results['silhouette'].isna().all():
            sil_opt = self.results.loc[self.results['silhouette'].idxmax(), 'k']
            recommendations.append(f"Silhouette: k={sil_opt}")
        
        # Calinski-Harabasz optimum
        if not self.results['calinski_harabasz'].isna().all():
            ch_opt = self.results.loc[self.results['calinski_harabasz'].idxmax(), 'k']
            recommendations.append(f"Calinski-Harabasz: k={ch_opt}")
        
        # Davies-Bouldin optimum
        if not self.results['davies_bouldin'].isna().all():
            db_opt = self.results.loc[self.results['davies_bouldin'].idxmin(), 'k']
            recommendations.append(f"Davies-Bouldin: k={db_opt}")
        
        # Gap statistic optimum
        if not self.results['gap_statistic'].isna().all():
            gap_opt = self.results.loc[self.results['gap_statistic'].idxmax(), 'k']
            recommendations.append(f"Gap Statistic: k={gap_opt}")
        
        # Temporal stability optimum
        if not self.results['temporal_ari_mean'].isna().all():
            temp_opt = self.results.loc[self.results['temporal_ari_mean'].idxmax(), 'k']
            recommendations.append(f"Temporal ARI: k={temp_opt}")
        
        axes[2,2].text(0.1, 0.9, 'Optimal k Recommendations:', fontsize=12, fontweight='bold', 
                      transform=axes[2,2].transAxes)
        
        for i, rec in enumerate(recommendations[:6]):  # Limit to 6 recommendations
            axes[2,2].text(0.1, 0.8 - i*0.1, rec, fontsize=10, 
                          transform=axes[2,2].transAxes)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        
        plt.show()
    
    def get_optimal_k_summary(self):
        """Get a comprehensive summary of optimal k values from bias-corrected metrics"""
        if self.results.empty:
            print("No results available. Run calculate_all_metrics() first.")
            return None
        
        summary = {}
        
        # Bias-corrected metrics (prioritized)
        if not self.results['penalized_silhouette'].isna().all():
            summary['penalized_silhouette_optimal'] = int(self.results.loc[self.results['penalized_silhouette'].idxmax(), 'k'])
            summary['penalized_silhouette_max_value'] = self.results['penalized_silhouette'].max()
        
        if not self.results['dvi_overall'].isna().all():
            summary['dvi_optimal'] = int(self.results.loc[self.results['dvi_overall'].idxmax(), 'k'])
            summary['dvi_max_value'] = self.results['dvi_overall'].max()
        
        if not self.results['temporal_ari_mean'].isna().all():
            summary['temporal_stability_optimal'] = int(self.results.loc[self.results['temporal_ari_mean'].idxmax(), 'k'])
            summary['temporal_stability_max_value'] = self.results['temporal_ari_mean'].max()
        
        if not self.results['procrustes_mean'].isna().all():
            summary['procrustes_optimal'] = int(self.results.loc[self.results['procrustes_mean'].idxmin(), 'k'])
            summary['procrustes_min_value'] = self.results['procrustes_mean'].min()
        
        # Relative improvement
        if self.relative_results:
            rel_df = pd.DataFrame(self.relative_results)
            if not rel_df.empty:
                summary['relative_improvement_optimal'] = int(rel_df.loc[rel_df['improvement_score'].idxmax(), 'k'])
                summary['relative_improvement_max_value'] = rel_df['improvement_score'].max()
        
        # Consensus recommendation
        decision_metrics = ['penalized_silhouette', 'dvi_overall', 'temporal_ari_mean']
        available_metrics = [m for m in decision_metrics if m in self.results.columns and not self.results[m].isna().all()]
        
        if available_metrics:
            decision_data = self.results[['k'] + available_metrics].dropna()
            if not decision_data.empty:
                # Normalize and average
                normalized_data = decision_data.copy()
                for metric in available_metrics:
                    values = decision_data[metric].values
                    normalized_data[metric] = (values - values.min()) / (values.max() - values.min() + 1e-10)
                
                consensus_scores = normalized_data.set_index('k')[available_metrics].mean(axis=1)
                summary['consensus_optimal'] = int(consensus_scores.idxmax())
                summary['consensus_score'] = consensus_scores.max()
        
        return summary
        
    def print_stability_analysis(self):
        """Print a detailed analysis of temporal stability findings"""
        if self.results.empty:
            print("No results available. Run calculate_all_metrics() first.")
            return
        
        print("="*80)
        print("WEATHER TYPING TEMPORAL STABILITY ANALYSIS")
        print("="*80)
        
        # Find most stable k values
        stability_metrics = {}
        
        if not self.results['temporal_ari_mean'].isna().all():
            best_ari_k = self.results.loc[self.results['temporal_ari_mean'].idxmax(), 'k']
            best_ari_val = self.results['temporal_ari_mean'].max()
            stability_metrics['Temporal ARI'] = (best_ari_k, best_ari_val, 'higher is better')
        
        if not self.results['frequency_stability'].isna().all():
            best_freq_k = self.results.loc[self.results['frequency_stability'].idxmin(), 'k']
            best_freq_val = self.results['frequency_stability'].min()
            stability_metrics['Frequency Stability'] = (best_freq_k, best_freq_val, 'lower CV is better')
        
        if not self.results['dvi_temporal'].isna().all():
            best_dvi_k = self.results.loc[self.results['dvi_temporal'].idxmax(), 'k']
            best_dvi_val = self.results['dvi_temporal'].max()
            stability_metrics['DVI Temporal'] = (best_dvi_k, best_dvi_val, 'higher is better')
        
        print("\nTEMPORAL STABILITY EVIDENCE:")
        print("-" * 40)
        for metric, (k, val, direction) in stability_metrics.items():
            print(f"{metric:20s}: k={k:2d} (value={val:.4f}, {direction})")
        
        # Check for consensus
        k_votes = {}
        for metric, (k, val, direction) in stability_metrics.items():
            if k not in k_votes:
                k_votes[k] = []
            k_votes[k].append(metric)
        
        print(f"\nSTABILITY CONSENSUS:")
        print("-" * 20)
        for k, supporting_metrics in sorted(k_votes.items()):
            print(f"k={k:2d}: supported by {', '.join(supporting_metrics)}")
        
        # Find the most supported k
        if k_votes:
            most_supported_k = max(k_votes.keys(), key=lambda x: len(k_votes[x]))
            print(f"\nMOST STABLE k: {most_supported_k} (supported by {len(k_votes[most_supported_k])} metrics)")
        
        print("\n" + "="*80)

# Example usage:
if __name__ == "__main__":
    # Method 1: Using your existing AtmosClusterer setup
    # Define paths and parameters (same as your example)
    latlon_path = "sample_data/mslp_CONUS_1979_2020.nc"
    variable_paths = {
        "h500": "sample_data/h_500_CONUS_1979_2020.nc",
        "mslp": "sample_data/era5_mslp_CONUS_1979_2020.nc",
        "u": "sample_data/u_850_CONUS_1979_2020.nc",
        "v": "sample_data/v_850_CONUS_1979_2020.nc",
    }
    variable_names = {
        "h500": "z",
        "mslp": "msl",
        "u": "u",
        "v": "v",
    }
    levels = {
        "h500": 500,
        "mslp": None,
        "u": 850,
        "v": 850,
    }
    
    # Initialize your clusterer and get reduced data
    # from your_module import AtmosClusterer  # Adjust import as needed
    # clusterer = AtmosClusterer(
    #     latlon_path=latlon_path,
    #     variable_paths=variable_paths,
    #     variable_names=variable_names,
    #     levels=levels,
    #     retain_variance=0.85
    # )
    # reduced_data = clusterer.run()
    # print(f"Reduced data shape: {reduced_data.shape}")
    
    # Initialize the validator with the reduced data
    validator = WeatherTypingValidator("CONUS_ERA5/CI_results.nc")
    
    # Method 1a: If you have the reduced_data array already
    validator.reduced_data = reduced_data
    
    # Method 1b: Setup validator with clusterer configuration (will run EOF internally)
    # validator.setup_with_clusterer(
    #     latlon_path=latlon_path,
    #     variable_paths=variable_paths,
    #     variable_names=variable_names,
    #     levels=levels,
    #     retain_variance=0.85
    # )
    
    # Method 2: If you want to run without the EOF data (temporal metrics only)
    print("Running without reduced data - only temporal stability metrics will be calculated")
    
    # Calculate all metrics
    results = validator.calculate_all_metrics(k_range=range(2, 36))
    
    # Create plots
    validator.plot_metrics(figsize=(15, 12), save_path="cluster_validation_metrics.png")
    
    # Get optimal k summary
    optimal_summary = validator.get_optimal_k_summary()
    print("\nOptimal k Summary:")
    for metric, value in optimal_summary.items():
        print(f"{metric}: {value}")
    
    # Save results to CSV
    results.to_csv("cluster_validation_results.csv", index=False)
    print("\nResults saved to cluster_validation_results.csv")
    
    print("\n" + "="*60)
    print("USAGE INSTRUCTIONS:")
    print("="*60)
    print("To use with your EOF data, uncomment the relevant sections above and either:")
    print("1. Pass reduced_data directly: validator.reduced_data = reduced_data")
    print("2. Use setup_with_clusterer() to run EOF analysis within the validator")
    print("3. The validator will calculate temporal stability metrics regardless")
    print("="*60)